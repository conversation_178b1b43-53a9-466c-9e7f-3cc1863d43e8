# 离线电脑运行问题解决方案

## 🔍 问题分析

### 现象描述
- **本机断网后**：运行 `run1.bat` 功能正常
- **离线电脑上**：运行 `run1.bat` 图标无法渲染，看板全是空白页
- **日志错误**：字符编码乱码、网络服务崩溃

### 根本原因分析

#### 1. 字符编码问题
```
'��式:' is not recognized as an internal or external command
'关键模块导入...' is not recognized as an internal or external command
```
- **原因**：不同Windows环境下的代码页设置不同
- **影响**：批处理文件中的中文字符显示异常

#### 2. WebEngine网络服务崩溃
```
[ERROR:network_service_instance_impl.cc(262)] Network service crashed, restarting service.
```
- **原因**：WebEngine在完全离线环境下仍尝试网络连接
- **影响**：图表渲染失败，看板显示空白

#### 3. 资源路径问题
- **本机路径**：`D:\AVTG0805-1`
- **离线机路径**：`D:\AVTG0805-3\AVTG0805-1`
- **原因**：路径结构差异导致资源定位失败
- **影响**：图标和JavaScript资源加载失败

#### 4. 环境配置差异
- 不同机器的系统环境变量、权限设置不同
- WebEngine在不同环境下的行为差异

## 🛠️ 解决方案

### 方案1：使用修复版启动脚本（推荐）

#### 步骤1：使用修复版启动脚本
```bash
# 使用修复版启动脚本
run1_fixed.bat
```

#### 修复版的改进：
1. **字符编码修复**
   - 强制设置UTF-8代码页
   - 优化中文字符处理

2. **WebEngine离线优化**
   - 完全禁用网络服务
   - 强化离线模式配置
   - 添加超强化Chromium标志

3. **资源路径增强**
   - 多路径查找机制
   - 自动创建缺失目录
   - 图标完整性检查

### 方案2：运行资源修复工具

#### 步骤1：运行离线资源修复
```bash
python tools/fix_offline_resources.py
```

#### 修复内容：
- 创建缺失的图标文件
- 设置嵌入式JavaScript资源
- 配置离线图表模式
- 创建WebEngine离线配置

#### 步骤2：运行环境测试
```bash
python tools/test_offline_environment.py
```

#### 测试内容：
- Python环境完整性
- 依赖包可用性
- 资源文件完整性
- 数据库访问权限
- WebEngine可用性
- 离线配置状态

### 方案3：手动修复（高级用户）

#### 1. 修复字符编码
```batch
@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion
```

#### 2. 设置WebEngine环境变量
```batch
set "QTWEBENGINE_DISABLE_SANDBOX=1"
set "QTWEBENGINE_DISABLE_NETWORK=1"
set "QTWEBENGINE_DISABLE_GPU=1"
set "QTWEBENGINE_OFFLINE_MODE=1"
set "QTWEBENGINE_CHROMIUM_FLAGS=--no-sandbox --disable-web-security --disable-network-service"
```

#### 3. 检查资源文件
确保以下文件存在：
```
src/resources/icons/
├── add.png
├── delete.png
├── refresh.png
├── logo.ico
└── ...

src/resources/js/
├── echarts.min.js
└── echarts-wordcloud.min.js
```

#### 4. 修复数据库权限
```batch
attrib -R "智能驾驶试验管控数据库.db"
attrib -R "app\tms_data.db"
attrib -R "data\test_management.db"
```

## 📋 完整操作步骤

### 在离线电脑上执行：

#### 步骤1：复制完整项目
确保复制了所有文件，特别是：
- `src/resources/` 目录
- `python/` 或 `.venv/` 目录
- 所有配置文件

#### 步骤2：使用修复版启动脚本
```bash
# 直接使用修复版
run1_fixed.bat
```

#### 步骤3：如果仍有问题，运行修复工具
```bash
# 修复资源
python tools/fix_offline_resources.py

# 测试环境
python tools/test_offline_environment.py
```

#### 步骤4：检查修复结果
查看生成的报告：
- `temp/offline_resource_fix_report.txt`
- `temp/offline_environment_test_report.txt`

## 🔧 高级配置

### WebEngine完全离线配置
如果需要更彻底的离线配置，可以修改代码：

```python
# 在主程序启动前添加
from src.utils.webengine_offline_optimizer import optimize_webengine_for_offline

# 优化WebEngine
optimize_webengine_for_offline()
```

### 嵌入式资源模式
启用完全嵌入式资源：

```python
# 使用嵌入式JavaScript资源
from src.utils.embedded_resources import get_embedded_chart_scripts

# 在HTML中使用嵌入式脚本
html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
</head>
<body>
    {get_embedded_chart_scripts()}
    <!-- 图表内容 -->
</body>
</html>
"""
```

## 📊 问题诊断

### 如果图标仍然无法显示：
1. 检查 `src/resources/icons/` 目录是否存在
2. 运行 `python tools/fix_offline_resources.py`
3. 检查防病毒软件是否阻止文件访问
4. 确保项目目录有完整读写权限

### 如果看板仍然空白：
1. 检查 `src/resources/js/` 目录中的JavaScript文件
2. 查看浏览器控制台错误信息
3. 运行 `python tools/test_offline_environment.py`
4. 尝试使用嵌入式资源模式

### 如果启动脚本报错：
1. 以管理员身份运行
2. 检查Python环境是否正确
3. 确保虚拟环境完整
4. 查看详细错误信息

## 📞 技术支持

### 常见问题排查清单：
- [ ] 使用了 `run1_fixed.bat` 启动脚本
- [ ] 运行了 `tools/fix_offline_resources.py`
- [ ] 检查了资源文件完整性
- [ ] 确认了数据库文件权限
- [ ] 测试了WebEngine可用性
- [ ] 查看了生成的测试报告

### 如果问题仍然存在：
请提供以下信息：
1. 操作系统版本和计算机名
2. 项目完整路径
3. `run1_fixed.bat` 的完整输出日志
4. `tools/test_offline_environment.py` 的测试报告
5. 错误截图

## 🎯 预防措施

### 部署离线环境时：
1. 使用 `tools/test_offline_environment.py` 验证环境
2. 确保所有资源文件完整复制
3. 使用修复版启动脚本
4. 设置正确的文件权限
5. 测试所有功能模块

### 定期维护：
1. 定期运行环境测试工具
2. 检查资源文件完整性
3. 更新离线资源配置
4. 备份重要配置文件

---

**总结**：通过使用修复版启动脚本和资源修复工具，可以解决离线环境下的图标渲染和看板显示问题。关键是确保字符编码正确、WebEngine完全离线配置、资源路径正确解析。
