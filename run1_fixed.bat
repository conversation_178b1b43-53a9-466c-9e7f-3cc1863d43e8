@echo off
:: 设置代码页为UTF-8，确保中文显示正确
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

:: ========================================
:: 智能驾驶试验管控工具 - 修复版启动脚本
:: 版本: 5.1 (离线环境优化版)
:: 日期: 2025-08-05
:: 功能: 完整的离线环境配置和应用启动
:: 修复: 字符编码、WebEngine离线配置、资源路径
:: ========================================

title 智能驾驶试验管控工具 - 启动器 v5.1 (离线优化版)

:: 显示启动横幅
echo.
echo ========================================
echo   智能驾驶试验管控工具
echo   启动器 v5.1 (离线优化版)
echo ========================================
echo 版本: 5.1 (完整离线版 - 修复版)
echo 功能: 自动环境配置、依赖验证、应用启动
echo 修复: 字符编码、WebEngine、资源路径
echo ========================================
echo.

:: 记录启动时间
set start_time=%time%
echo 启动时间: %start_time%
echo.

:: 获取脚本所在目录作为项目根目录
set "PROJECT_ROOT=%~dp0"
set "PROJECT_ROOT=%PROJECT_ROOT:~0,-1%"
cd /d "%PROJECT_ROOT%"

echo [信息] 项目根目录: %PROJECT_ROOT%
echo.

:: ========================================
:: 第1步: 检查基础环境
:: ========================================
echo [第1步/7] 检查基础环境...
echo 当前目录: %CD%
echo 操作系统: %OS%
echo 计算机名: %COMPUTERNAME%
echo 用户名: %USERNAME%
echo.

:: ========================================
:: 第2步: 配置Python解释器路径
:: ========================================
echo [第2步/7] 配置Python解释器路径...

:: 优先使用项目内置的Python解释器
set "PYTHON_CMD="
if exist "%PROJECT_ROOT%\python\python.exe" (
    set "PYTHON_CMD=%PROJECT_ROOT%\python\python.exe"
    echo [成功] 找到项目内置Python解释器
) else (
    :: 尝试使用虚拟环境中的Python
    if exist "%PROJECT_ROOT%\.venv\Scripts\python.exe" (
        set "PYTHON_CMD=%PROJECT_ROOT%\.venv\Scripts\python.exe"
        echo [成功] 找到虚拟环境Python解释器
    ) else (
        :: 尝试使用系统Python
        where python >nul 2>&1
        if !errorlevel! equ 0 (
            set "PYTHON_CMD=python"
            echo [警告] 使用系统Python解释器
        ) else (
            echo [错误] 未找到可用的Python解释器
            echo [提示] 请确保以下任一条件满足:
            echo   1. 项目根目录存在 python\python.exe
            echo   2. 虚拟环境 .venv\Scripts\python.exe 存在
            echo   3. 系统已安装Python并添加到PATH
            goto :error_exit
        )
    )
)

:: 验证Python版本
echo [信息] 验证Python版本...
"%PYTHON_CMD%" --version
if !errorlevel! neq 0 (
    echo [错误] Python解释器无法正常运行
    goto :error_exit
)
echo.

:: ========================================
:: 第3步: 虚拟环境配置
:: ========================================
echo [第3步/7] 配置虚拟环境...

:: 检查虚拟环境是否存在
if not exist "%PROJECT_ROOT%\.venv" (
    echo [警告] 虚拟环境不存在，正在创建...
    "%PYTHON_CMD%" -m venv "%PROJECT_ROOT%\.venv"
    if !errorlevel! neq 0 (
        echo [错误] 虚拟环境创建失败
        echo [提示] 请检查Python安装是否完整，或手动运行:
        echo   "%PYTHON_CMD%" -m venv .venv
        goto :error_exit
    )
    echo [成功] 虚拟环境创建完成
) else (
    echo [信息] 虚拟环境已存在
)

:: 检查虚拟环境完整性
if not exist "%PROJECT_ROOT%\.venv\Scripts\python.exe" (
    echo [错误] 虚拟环境不完整，缺少Python解释器
    echo [提示] 请删除 .venv 目录后重新运行此脚本
    goto :error_exit
)

if not exist "%PROJECT_ROOT%\.venv\Scripts\activate.bat" (
    echo [错误] 虚拟环境不完整，缺少激活脚本
    echo [提示] 请删除 .venv 目录后重新运行此脚本
    goto :error_exit
)

echo [成功] 虚拟环境检查通过
echo.

:: ========================================
:: 第4步: 设置环境变量和数据库权限
:: ========================================
echo [第4步/7] 设置环境变量和数据库权限...

:: 激活虚拟环境
call "%PROJECT_ROOT%\.venv\Scripts\activate.bat"
if !errorlevel! neq 0 (
    echo [错误] 虚拟环境激活失败
    goto :error_exit
)

:: 设置项目路径到PYTHONPATH
set "PYTHONPATH=%PROJECT_ROOT%;%PROJECT_ROOT%\src;%PYTHONPATH%"

:: 设置工作目录
set "WORKING_DIR=%PROJECT_ROOT%"

:: 优化Python环境变量
set "PYTHONIOENCODING=utf-8"
set "PYTHONUNBUFFERED=1"

:: 设置Qt环境变量（解决Qt平台插件问题）
set "QT_PLUGIN_PATH=%PROJECT_ROOT%\.venv\Lib\site-packages\PyQt5\Qt5\plugins"
set "QT_QPA_PLATFORM_PLUGIN_PATH=%PROJECT_ROOT%\.venv\Lib\site-packages\PyQt5\Qt5\plugins\platforms"
set "PATH=%PROJECT_ROOT%\.venv\Lib\site-packages\PyQt5\Qt5\bin;%PATH%"

:: ========================================
:: 修复WebEngine网络服务崩溃问题（超强化版 - 离线优化）
:: ========================================
echo [信息] 应用WebEngine网络服务崩溃修复（离线优化版）...

:: 核心沙盒和网络完全禁用
set "QTWEBENGINE_DISABLE_SANDBOX=1"
set "QTWEBENGINE_DISABLE_GPU_SANDBOX=1"
set "QTWEBENGINE_REMOTE_DEBUGGING=0"
set "QTWEBENGINE_DISABLE_NETWORK=1"
set "QTWEBENGINE_DISABLE_BACKGROUND_NETWORKING=1"
set "QTWEBENGINE_DISABLE_SYNC=1"

:: GPU和硬件加速完全禁用（离线环境优化）
set "QTWEBENGINE_DISABLE_GPU=1"
set "QTWEBENGINE_DISABLE_SOFTWARE_RASTERIZER=1"
set "QTWEBENGINE_DISABLE_ACCELERATED_2D_CANVAS=1"
set "QTWEBENGINE_DISABLE_WEBGL=1"

:: 扩展和插件完全禁用
set "QTWEBENGINE_DISABLE_EXTENSIONS=1"
set "QTWEBENGINE_DISABLE_PLUGINS=1"

:: 超强化Chromium标志（完全离线环境 - 优化版）
set "QTWEBENGINE_CHROMIUM_FLAGS=--no-sandbox --disable-web-security --disable-features=VizDisplayCompositor --disable-gpu --disable-software-rasterizer --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-renderer-backgrounding --disable-extensions --disable-sync --no-first-run --disable-logging --disable-background-networking --in-process-gpu --disable-dev-shm-usage --disable-features=TranslateUI --disable-ipc-flooding-protection --disable-default-apps --disable-component-extensions-with-background-pages --disable-background-mode --disable-client-side-phishing-detection --disable-hang-monitor --disable-prompt-on-repost --disable-domain-reliability --disable-component-update --disable-background-downloads --no-default-browser-check --no-pings --disable-translate --disable-features=MediaRouter --disable-features=PasswordManager --disable-features=AutofillServerCommunication --disable-network-service --disable-features=NetworkService --disable-features=NetworkServiceInProcess"

:: 设置本地数据目录（避免系统目录权限问题）
set "QTWEBENGINE_USER_DATA_DIR=%PROJECT_ROOT%\temp\webengine_data"
if not exist "%QTWEBENGINE_USER_DATA_DIR%" mkdir "%QTWEBENGINE_USER_DATA_DIR%" 2>nul

:: 新增：强制离线模式环境变量
set "QTWEBENGINE_OFFLINE_MODE=1"
set "QTWEBENGINE_FORCE_LOCAL_RESOURCES=1"

echo [成功] WebEngine网络服务崩溃修复已应用（离线优化版）
echo   - 沙盒模式: 完全禁用
echo   - 网络服务: 完全禁用
echo   - GPU功能: 完全禁用
echo   - 扩展插件: 完全禁用
echo   - 后台服务: 完全禁用
echo   - 离线模式: 强制启用

:: 设置多进程相关环境变量
for /f %%i in ('wmic cpu get NumberOfCores /value ^| find "="') do set %%i
set "OMP_NUM_THREADS=%NumberOfCores%"
set "MKL_NUM_THREADS=%NumberOfCores%"
set "NUMEXPR_NUM_THREADS=%NumberOfCores%"

:: ========================================
:: 数据库权限修复 - 关键修复部分
:: ========================================
echo [信息] 检查和修复数据库文件权限...

:: 检查主数据库文件
set "MAIN_DB=%PROJECT_ROOT%\智能驾驶试验管控数据库.db"
if exist "%MAIN_DB%" (
    echo [信息] 找到主数据库文件: %MAIN_DB%

    :: 移除只读属性
    attrib -R "%MAIN_DB%" 2>nul
    if !errorlevel! equ 0 (
        echo [成功] 已移除数据库文件只读属性
    ) else (
        echo [警告] 无法移除数据库文件只读属性，可能需要管理员权限
    )

    :: 检查并处理WAL和SHM文件
    if exist "%MAIN_DB%-wal" (
        attrib -R "%MAIN_DB%-wal" 2>nul
        echo [信息] 已处理WAL文件权限
    )
    if exist "%MAIN_DB%-shm" (
        attrib -R "%MAIN_DB%-shm" 2>nul
        echo [信息] 已处理SHM文件权限
    )
) else (
    echo [信息] 主数据库文件不存在，正在创建...
    echo [信息] 程序运行时会自动创建数据库文件
)

:: 检查app目录下的数据库文件
set "APP_DB=%PROJECT_ROOT%\app\tms_data.db"
if exist "%APP_DB%" (
    echo [信息] 找到应用数据库文件: %APP_DB%
    attrib -R "%APP_DB%" 2>nul
    echo [成功] 已处理应用数据库文件权限
)

:: 检查data目录下的数据库文件
set "DATA_DB=%PROJECT_ROOT%\data\test_management.db"
if exist "%DATA_DB%" (
    echo [信息] 找到测试数据库文件: %DATA_DB%
    attrib -R "%DATA_DB%" 2>nul
    echo [成功] 已处理测试数据库文件权限
)

:: 确保关键目录具有写入权限
echo [信息] 确保关键目录权限...
if not exist "%PROJECT_ROOT%\app" mkdir "%PROJECT_ROOT%\app" 2>nul
if not exist "%PROJECT_ROOT%\data" mkdir "%PROJECT_ROOT%\data" 2>nul
if not exist "%PROJECT_ROOT%\temp" mkdir "%PROJECT_ROOT%\temp" 2>nul

:: 设置临时目录环境变量，避免系统临时目录权限问题
set "TEMP=%PROJECT_ROOT%\temp"
set "TMP=%PROJECT_ROOT%\temp"

echo [成功] 环境变量和数据库权限配置完成
echo   - PYTHONPATH: %PYTHONPATH%
echo   - 工作目录: %WORKING_DIR%
echo   - CPU核心数: %NumberOfCores%
echo   - Qt插件路径: %QT_PLUGIN_PATH%
echo   - 临时目录: %TEMP%
echo.

:: ========================================
:: 第5步: 强化离线环境配置（优化版）
:: ========================================
echo [第5步/7] 强化离线环境配置（优化版）...

:: 检查ECharts本地资源（更新为正确路径）
set "ECHARTS_CORE_FILE=%PROJECT_ROOT%\src\resources\js\echarts.min.js"
set "ECHARTS_WORDCLOUD_FILE=%PROJECT_ROOT%\src\resources\js\echarts-wordcloud.min.js"

if exist "%ECHARTS_CORE_FILE%" (
    if exist "%ECHARTS_WORDCLOUD_FILE%" (
        echo [成功] ECharts本地资源完整存在
        echo [信息] - ECharts主库: %ECHARTS_CORE_FILE%
        echo [信息] - 词云插件: %ECHARTS_WORDCLOUD_FILE%
        echo [信息] pyecharts将自动使用本地资源

        :: 设置离线模式配置
        echo [信息] 配置PyEcharts离线模式...
        "%PYTHON_CMD%" -c "from src.utils.offline_chart_config import setup_offline_charts; print('离线配置结果:', setup_offline_charts())" 2>nul
        if !errorlevel! equ 0 (
            echo [成功] PyEcharts离线模式配置完成
        ) else (
            echo [警告] PyEcharts离线模式配置失败，将使用默认配置
        )
    ) else (
        echo [警告] ECharts词云插件不存在: %ECHARTS_WORDCLOUD_FILE%
        echo [提示] 词云图功能可能需要网络连接
    )
) else (
    echo [警告] ECharts本地资源不存在: %ECHARTS_CORE_FILE%
    echo [提示] 建议运行以下命令获取完整离线图表功能:
    echo [提示]   python tools/download_offline_resources.py
    echo [信息] 图表功能将尝试使用在线资源（需要网络连接）
)

:: 新增：检查图标资源完整性
echo [信息] 检查图标资源完整性...
set "ICONS_DIR=%PROJECT_ROOT%\src\resources\icons"
if exist "%ICONS_DIR%" (
    echo [成功] 图标资源目录存在: %ICONS_DIR%

    :: 检查关键图标文件
    set "missing_icons="
    if not exist "%ICONS_DIR%\add.png" set "missing_icons=!missing_icons! add.png"
    if not exist "%ICONS_DIR%\delete.png" set "missing_icons=!missing_icons! delete.png"
    if not exist "%ICONS_DIR%\refresh.png" set "missing_icons=!missing_icons! refresh.png"
    if not exist "%ICONS_DIR%\logo.ico" set "missing_icons=!missing_icons! logo.ico"

    if "!missing_icons!"=="" (
        echo [成功] 关键图标文件完整
    ) else (
        echo [警告] 缺少图标文件:!missing_icons!
        echo [信息] 程序将使用默认图标替代
    )
) else (
    echo [警告] 图标资源目录不存在: %ICONS_DIR%
    echo [信息] 程序将使用系统默认图标
)

echo.

:: ========================================
:: 第6步: 检查关键依赖和数据库连接（优化版）
:: ========================================
echo [第6步/7] 检查关键依赖和数据库连接（优化版）...

:: 简化的依赖检查（修复字符编码问题）
echo [信息] 测试关键模块导入...
"%PYTHON_CMD%" -c "import sys; print('Python版本: ' + sys.version)" 2>nul
if !errorlevel! neq 0 (
    echo [错误] Python基础功能异常
    goto :error_exit
)

:: 检查关键依赖包
set "missing_deps="
"%PYTHON_CMD%" -c "import PyQt5.QtWidgets" 2>nul
if !errorlevel! neq 0 set "missing_deps=!missing_deps! PyQt5"

"%PYTHON_CMD%" -c "import pandas" 2>nul
if !errorlevel! neq 0 set "missing_deps=!missing_deps! pandas"

"%PYTHON_CMD%" -c "import numpy" 2>nul
if !errorlevel! neq 0 set "missing_deps=!missing_deps! numpy"

"%PYTHON_CMD%" -c "import matplotlib" 2>nul
if !errorlevel! neq 0 set "missing_deps=!missing_deps! matplotlib"

"%PYTHON_CMD%" -c "import pyecharts" 2>nul
if !errorlevel! neq 0 set "missing_deps=!missing_deps! pyecharts"

"%PYTHON_CMD%" -c "import openpyxl" 2>nul
if !errorlevel! neq 0 set "missing_deps=!missing_deps! openpyxl"

"%PYTHON_CMD%" -c "import xlrd" 2>nul
if !errorlevel! neq 0 set "missing_deps=!missing_deps! xlrd"

"%PYTHON_CMD%" -c "import PIL" 2>nul
if !errorlevel! neq 0 set "missing_deps=!missing_deps! Pillow"

"%PYTHON_CMD%" -c "import pyqtgraph" 2>nul
if !errorlevel! neq 0 set "missing_deps=!missing_deps! pyqtgraph"

:: 特别检查SQLite数据库连接（修复字符编码问题）
echo [信息] 测试SQLite数据库连接...
"%PYTHON_CMD%" -c "import sqlite3; conn = sqlite3.connect(':memory:'); conn.close(); print('SQLite连接测试成功')" 2>nul
if !errorlevel! neq 0 (
    echo [错误] SQLite数据库连接测试失败
    goto :error_exit
) else (
    echo [成功] SQLite数据库连接测试通过
)

:: 简化的数据库检查（避免复杂的权限测试导致脚本退出）
if exist "%MAIN_DB%" (
    echo [信息] 主数据库文件存在: %MAIN_DB%
    attrib -R "%MAIN_DB%" 2>nul
    echo [成功] 数据库文件权限检查完成
) else (
    echo [信息] 主数据库文件不存在，程序运行时会自动创建
)

:: 特别检查Qt平台插件
echo [信息] 验证Qt平台插件...
if exist "%QT_PLUGIN_PATH%\platforms\qwindows.dll" (
    echo [成功] Qt Windows平台插件已找到
) else (
    echo [警告] Qt Windows平台插件未找到，GUI可能无法正常显示
)

if not "!missing_deps!"=="" (
    echo [警告] 检测到缺失的依赖包:!missing_deps!
    echo [提示] 如果程序运行异常，请手动安装所需依赖
    echo [提示] 命令: pip install -r requirements.txt
) else (
    echo [成功] 所有关键依赖检查通过
)
echo.

:: ========================================
:: 第7步: 启动应用程序（优化版）
:: ========================================
echo [第7步/7] 启动应用程序（优化版）...

:: 检查run.py文件是否存在
if not exist "%PROJECT_ROOT%\run.py" (
    echo [错误] 找不到应用程序入口文件: run.py
    echo [提示] 请确保在正确的项目目录中运行此脚本
    goto :error_exit
)

:: 显示启动信息
echo [信息] 正在启动智能驾驶试验管控工具...
echo [信息] 入口文件: %PROJECT_ROOT%\run.py
echo [信息] Python解释器: %PYTHON_CMD%
echo [信息] 工作目录: %WORKING_DIR%
echo [信息] 离线模式: 已启用
echo.

:: 记录启动完成时间
set end_time=%time%
echo 环境配置完成时间: %end_time%
echo.

echo ========================================
echo 正在启动应用程序，请稍候...
echo ========================================
echo.

:: 启动应用程序
cd /d "%WORKING_DIR%"
"%PYTHON_CMD%" "%PROJECT_ROOT%\run.py"

:: 检查应用程序退出状态
set "app_exit_code=%errorlevel%"
echo.
echo ========================================
echo 应用程序已退出
echo ========================================

if !app_exit_code! equ 0 (
    echo [信息] 应用程序正常退出 (退出代码: !app_exit_code!)
) else (
    echo [警告] 应用程序异常退出 (退出代码: !app_exit_code!)
    echo [提示] 如果遇到问题，请查看错误信息或联系技术支持
)

echo.
echo 感谢使用智能驾驶试验管控工具！
echo.
pause
exit /b !app_exit_code!

:: ========================================
:: 错误处理和帮助信息（优化版）
:: ========================================
:error_exit
echo.
echo ========================================
echo 启动失败 - 错误处理（优化版）
echo ========================================
echo.
echo [错误] 应用程序启动失败，请根据上述提示解决问题。
echo.
echo [常见解决方案]
echo 1. 环境问题:
echo    - 确保项目文件完整，包含 python\ 目录或 .venv\ 目录
echo    - 检查Python版本是否为3.7或更高版本
echo    - 确保有足够的磁盘空间和内存
echo.
echo 2. 依赖问题:
echo    - 运行以下命令手动安装依赖:
echo      python -m pip install -r requirements.txt
echo    - 或者重新创建虚拟环境:
echo      rmdir /s /q .venv
echo      python -m venv .venv
echo      .venv\Scripts\activate.bat
echo      pip install -r requirements.txt
echo.
echo 3. 权限问题:
echo    - 以管理员身份运行此脚本
echo    - 检查防病毒软件是否阻止了程序运行
echo    - 确保项目目录有读写权限
echo.
echo 4. 离线环境特殊问题:
echo    - 确保所有资源文件已正确复制
echo    - 检查图标和JavaScript资源是否完整
echo    - 验证WebEngine离线配置是否正确
echo    - 如果图标显示异常，检查 src\resources\icons 目录
echo    - 如果图表显示异常，检查 src\resources\js 目录
echo.
echo 5. 数据库权限问题:
echo    - 如果出现 "attempt to write a readonly database" 错误:
echo      * 以管理员身份运行此脚本 (推荐)
echo      * 右键点击数据库文件 → 属性 → 取消"只读"勾选
echo      * 检查防病毒软件是否阻止了数据库文件访问
echo      * 确保项目目录不在受保护的系统目录中
echo.
echo [技术支持]
echo 如果问题仍然存在，请联系技术支持并提供以下信息:
echo - 操作系统版本: %OS%
echo - 项目路径: %PROJECT_ROOT%
echo - 错误发生的步骤
echo - 完整的错误信息截图
echo.
echo ========================================
pause
exit /b 1
