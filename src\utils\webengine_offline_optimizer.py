#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
WebEngine离线优化器

该模块专门用于优化WebEngine在离线环境下的配置和性能，
解决网络服务崩溃、资源加载失败等问题。

作者: AI Assistant
创建时间: 2025-08-05
"""

import os
import sys
import logging
from typing import Dict, List, Optional, Any
from pathlib import Path

try:
    from PyQt5.QtCore import QUrl, QStandardPaths
    from PyQt5.QtWebEngineWidgets import QWebEngineView, QWebEngineSettings, QWebEngineProfile
    from PyQt5.QtWebEngineCore import QWebEngineUrlScheme
    WEBENGINE_AVAILABLE = True
except ImportError:
    WEBENGINE_AVAILABLE = False

logger = logging.getLogger(__name__)


class WebEngineOfflineOptimizer:
    """WebEngine离线优化器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.project_root = self._get_project_root()
        self.optimizations_applied = []
        
    def _get_project_root(self) -> Path:
        """获取项目根目录"""
        current_file = Path(__file__)
        # 从 src/utils/webengine_offline_optimizer.py 向上两级到项目根目录
        return current_file.parent.parent.parent
    
    def setup_environment_variables(self) -> bool:
        """设置WebEngine环境变量"""
        self.logger.info("🔧 设置WebEngine环境变量...")
        
        try:
            # 核心离线配置
            env_vars = {
                # 沙盒和安全
                "QTWEBENGINE_DISABLE_SANDBOX": "1",
                "QTWEBENGINE_DISABLE_GPU_SANDBOX": "1",
                
                # 网络完全禁用
                "QTWEBENGINE_DISABLE_NETWORK": "1",
                "QTWEBENGINE_DISABLE_BACKGROUND_NETWORKING": "1",
                "QTWEBENGINE_DISABLE_SYNC": "1",
                
                # GPU和硬件加速禁用
                "QTWEBENGINE_DISABLE_GPU": "1",
                "QTWEBENGINE_DISABLE_SOFTWARE_RASTERIZER": "1",
                "QTWEBENGINE_DISABLE_ACCELERATED_2D_CANVAS": "1",
                "QTWEBENGINE_DISABLE_WEBGL": "1",
                
                # 扩展和插件禁用
                "QTWEBENGINE_DISABLE_EXTENSIONS": "1",
                "QTWEBENGINE_DISABLE_PLUGINS": "1",
                
                # 调试和日志
                "QTWEBENGINE_REMOTE_DEBUGGING": "0",
                
                # 自定义离线标志
                "QTWEBENGINE_OFFLINE_MODE": "1",
                "QTWEBENGINE_FORCE_LOCAL_RESOURCES": "1",
            }
            
            # 设置用户数据目录
            user_data_dir = self.project_root / "temp" / "webengine_data"
            user_data_dir.mkdir(parents=True, exist_ok=True)
            env_vars["QTWEBENGINE_USER_DATA_DIR"] = str(user_data_dir)
            
            # 应用环境变量
            for key, value in env_vars.items():
                os.environ[key] = value
                self.logger.debug(f"设置环境变量: {key}={value}")
            
            # 设置Chromium命令行标志
            chromium_flags = [
                "--no-sandbox",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",
                "--disable-gpu",
                "--disable-software-rasterizer",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--disable-extensions",
                "--disable-sync",
                "--no-first-run",
                "--disable-logging",
                "--disable-background-networking",
                "--in-process-gpu",
                "--disable-dev-shm-usage",
                "--disable-features=TranslateUI",
                "--disable-ipc-flooding-protection",
                "--disable-default-apps",
                "--disable-component-extensions-with-background-pages",
                "--disable-background-mode",
                "--disable-client-side-phishing-detection",
                "--disable-hang-monitor",
                "--disable-prompt-on-repost",
                "--disable-domain-reliability",
                "--disable-component-update",
                "--disable-background-downloads",
                "--no-default-browser-check",
                "--no-pings",
                "--disable-translate",
                "--disable-features=MediaRouter",
                "--disable-features=PasswordManager",
                "--disable-features=AutofillServerCommunication",
                "--disable-network-service",
                "--disable-features=NetworkService",
                "--disable-features=NetworkServiceInProcess"
            ]
            
            os.environ["QTWEBENGINE_CHROMIUM_FLAGS"] = " ".join(chromium_flags)
            
            self.optimizations_applied.append("设置WebEngine环境变量")
            self.logger.info("✅ WebEngine环境变量设置完成")
            return True
            
        except Exception as e:
            self.logger.error(f"设置WebEngine环境变量失败: {e}")
            return False
    
    def optimize_webengine_view(self, web_view: Any) -> bool:
        """优化WebEngineView实例"""
        if not WEBENGINE_AVAILABLE:
            self.logger.warning("WebEngine不可用，跳过优化")
            return False
        
        self.logger.info("🔧 优化WebEngineView实例...")
        
        try:
            # 获取页面和设置
            page = web_view.page()
            if not page:
                self.logger.warning("无法获取WebEngine页面")
                return False
            
            settings = page.settings()
            if not settings:
                self.logger.warning("无法获取WebEngine设置")
                return False
            
            # 基本设置优化
            settings.setAttribute(QWebEngineSettings.JavascriptEnabled, True)
            settings.setAttribute(QWebEngineSettings.LocalContentCanAccessRemoteUrls, False)
            settings.setAttribute(QWebEngineSettings.LocalContentCanAccessFileUrls, True)
            settings.setAttribute(QWebEngineSettings.AllowRunningInsecureContent, True)
            
            # 禁用不必要的功能
            settings.setAttribute(QWebEngineSettings.AutoLoadImages, True)
            settings.setAttribute(QWebEngineSettings.PluginsEnabled, False)
            settings.setAttribute(QWebEngineSettings.JavascriptCanOpenWindows, False)
            settings.setAttribute(QWebEngineSettings.JavascriptCanAccessClipboard, False)
            settings.setAttribute(QWebEngineSettings.LinksIncludedInFocusChain, False)
            settings.setAttribute(QWebEngineSettings.LocalStorageEnabled, True)
            settings.setAttribute(QWebEngineSettings.XSSAuditingEnabled, False)
            settings.setAttribute(QWebEngineSettings.SpatialNavigationEnabled, False)
            settings.setAttribute(QWebEngineSettings.HyperlinkAuditingEnabled, False)
            settings.setAttribute(QWebEngineSettings.ScrollAnimatorEnabled, False)
            settings.setAttribute(QWebEngineSettings.ErrorPageEnabled, False)
            
            # 性能优化设置
            settings.setAttribute(QWebEngineSettings.Accelerated2dCanvasEnabled, False)
            settings.setAttribute(QWebEngineSettings.WebGLEnabled, False)
            settings.setAttribute(QWebEngineSettings.ShowScrollBars, False)
            
            # 获取配置文件并优化
            profile = page.profile()
            if profile:
                # 设置离线存储策略
                profile.setPersistentStoragePath(str(self.project_root / "temp" / "webengine_storage"))
                profile.setCachePath(str(self.project_root / "temp" / "webengine_cache"))
                
                # 禁用HTTP缓存（避免网络请求）
                profile.setHttpCacheType(QWebEngineProfile.NoCache)
                
                self.logger.debug("WebEngine配置文件优化完成")
            
            self.optimizations_applied.append("优化WebEngineView实例")
            self.logger.info("✅ WebEngineView实例优化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"优化WebEngineView实例失败: {e}")
            return False
    
    def create_offline_html_template(self) -> str:
        """创建离线HTML模板"""
        self.logger.info("🔧 创建离线HTML模板...")
        
        try:
            # 检查是否有嵌入式资源
            embedded_scripts = ""
            try:
                from .embedded_resources import get_embedded_chart_scripts
                embedded_scripts = get_embedded_chart_scripts()
                self.logger.info("使用嵌入式JavaScript资源")
            except ImportError:
                self.logger.warning("嵌入式资源不可用，使用本地文件引用")
                # 使用本地文件引用
                js_dir = self.project_root / "src" / "resources" / "js"
                echarts_file = js_dir / "echarts.min.js"
                wordcloud_file = js_dir / "echarts-wordcloud.min.js"
                
                if echarts_file.exists() and wordcloud_file.exists():
                    embedded_scripts = f'''
                    <script src="file:///{echarts_file.as_posix()}"></script>
                    <script src="file:///{wordcloud_file.as_posix()}"></script>
                    '''
                else:
                    self.logger.warning("本地JavaScript文件不存在，使用内联备用方案")
                    embedded_scripts = '''
                    <script>
                    // 备用方案：创建基本的图表容器
                    window.echarts = window.echarts || {
                        init: function(container) {
                            return {
                                setOption: function(option) {
                                    container.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">图表数据加载中...</div>';
                                },
                                resize: function() {},
                                dispose: function() {}
                            };
                        }
                    };
                    </script>
                    '''
            
            # 创建完整的HTML模板
            html_template = f'''
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>离线图表显示</title>
                <style>
                    body {{
                        margin: 0;
                        padding: 0;
                        font-family: "Microsoft YaHei", Arial, sans-serif;
                        background-color: #f5f5f5;
                    }}
                    .chart-container {{
                        width: 100%;
                        height: 100vh;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }}
                    .loading {{
                        text-align: center;
                        color: #666;
                        font-size: 14px;
                    }}
                    .error {{
                        text-align: center;
                        color: #e74c3c;
                        font-size: 14px;
                    }}
                </style>
            </head>
            <body>
                <div id="main" class="chart-container">
                    <div class="loading">正在加载图表...</div>
                </div>
                
                {embedded_scripts}
                
                <script>
                // 离线模式增强脚本
                (function() {{
                    console.log('🔧 离线模式图表脚本已加载');
                    
                    // 禁用所有网络请求
                    if (typeof XMLHttpRequest !== 'undefined') {{
                        const originalOpen = XMLHttpRequest.prototype.open;
                        XMLHttpRequest.prototype.open = function() {{
                            console.warn('网络请求已被阻止（离线模式）');
                            return false;
                        }};
                    }}
                    
                    if (typeof fetch !== 'undefined') {{
                        window.fetch = function() {{
                            console.warn('Fetch请求已被阻止（离线模式）');
                            return Promise.reject(new Error('离线模式：网络请求被阻止'));
                        }};
                    }}
                    
                    // 确保ECharts可用
                    if (typeof echarts === 'undefined') {{
                        console.error('ECharts未加载，使用备用显示');
                        document.getElementById('main').innerHTML = 
                            '<div class="error">图表组件未加载<br>请检查离线资源配置</div>';
                    }} else {{
                        console.log('✅ ECharts已就绪');
                    }}
                    
                    // 全局错误处理
                    window.addEventListener('error', function(e) {{
                        console.error('页面错误:', e.message);
                    }});
                    
                    window.addEventListener('unhandledrejection', function(e) {{
                        console.error('未处理的Promise错误:', e.reason);
                    }});
                }})();
                </script>
            </body>
            </html>
            '''
            
            self.optimizations_applied.append("创建离线HTML模板")
            self.logger.info("✅ 离线HTML模板创建完成")
            return html_template
            
        except Exception as e:
            self.logger.error(f"创建离线HTML模板失败: {e}")
            return self._get_fallback_html()
    
    def _get_fallback_html(self) -> str:
        """获取备用HTML"""
        return '''
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>离线模式</title>
        </head>
        <body>
            <div style="padding: 20px; text-align: center; color: #666;">
                <h3>离线模式</h3>
                <p>图表功能暂时不可用</p>
            </div>
        </body>
        </html>
        '''
    
    def apply_all_optimizations(self, web_view: Optional[Any] = None) -> Dict[str, bool]:
        """应用所有优化"""
        self.logger.info("🚀 开始应用WebEngine离线优化...")
        
        results = {
            "environment_vars": False,
            "webengine_view": False,
            "html_template": False,
            "overall_success": False
        }
        
        # 1. 设置环境变量
        results["environment_vars"] = self.setup_environment_variables()
        
        # 2. 优化WebEngineView（如果提供）
        if web_view and WEBENGINE_AVAILABLE:
            results["webengine_view"] = self.optimize_webengine_view(web_view)
        else:
            results["webengine_view"] = True  # 如果没有提供view，认为成功
        
        # 3. 创建HTML模板
        html_template = self.create_offline_html_template()
        results["html_template"] = bool(html_template)
        
        # 计算整体成功率
        success_count = sum(1 for success in results.values() if success)
        results["overall_success"] = success_count >= 2  # 至少2项成功
        
        if results["overall_success"]:
            self.logger.info("✅ WebEngine离线优化完成")
        else:
            self.logger.warning("⚠️ WebEngine离线优化部分失败")
        
        return results
    
    def get_optimization_report(self) -> str:
        """获取优化报告"""
        report = []
        report.append("WebEngine离线优化报告")
        report.append("=" * 40)
        report.append(f"项目路径: {self.project_root}")
        report.append(f"WebEngine可用: {'是' if WEBENGINE_AVAILABLE else '否'}")
        report.append("")
        
        if self.optimizations_applied:
            report.append("已应用的优化:")
            for opt in self.optimizations_applied:
                report.append(f"  ✅ {opt}")
        else:
            report.append("未应用任何优化")
        
        report.append("")
        report.append("建议:")
        report.append("1. 确保使用修复版启动脚本 (run1_fixed.bat)")
        report.append("2. 检查JavaScript资源文件是否完整")
        report.append("3. 如果问题持续，尝试清除WebEngine缓存")
        
        return "\n".join(report)


# 全局优化器实例
webengine_optimizer = WebEngineOfflineOptimizer()


def optimize_webengine_for_offline(web_view: Optional[Any] = None) -> bool:
    """
    为离线环境优化WebEngine的便捷函数
    
    参数:
        web_view: 可选的QWebEngineView实例
        
    返回:
        bool: 优化是否成功
    """
    results = webengine_optimizer.apply_all_optimizations(web_view)
    return results["overall_success"]


def get_offline_html_template() -> str:
    """
    获取离线HTML模板的便捷函数
    
    返回:
        str: HTML模板字符串
    """
    return webengine_optimizer.create_offline_html_template()
