#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import logging
from PyQt5.QtGui import QIcon, QPixmap

class ResourceLoader:
    """资源加载器，用于加载图标、样式表等资源"""
    # 缓存已加载的资源，避免重复加载
    _icon_cache = {}
    _pixmap_cache = {}
    _stylesheet_cache = {}
    # 缓存已检查过的缺失文件，避免重复警告
    _missing_files_cache = set()

    @staticmethod
    def get_app_root_path():
        """获取应用程序根目录的绝对路径（增强版）"""
        # 如果是冻结的可执行文件
        if getattr(sys, 'frozen', False):
            return os.path.dirname(sys.executable)

        # 尝试多种方法确定项目根目录
        current_file = os.path.abspath(__file__)

        # 方法1: 从当前文件向上查找（标准方法）
        root_path = os.path.abspath(os.path.join(os.path.dirname(current_file), '..', '..'))

        # 方法2: 检查环境变量中的项目路径
        if 'PROJECT_ROOT' in os.environ:
            env_root = os.environ['PROJECT_ROOT']
            if os.path.exists(env_root) and os.path.isdir(env_root):
                root_path = env_root

        # 方法3: 向上查找包含特征文件的目录
        search_path = os.path.dirname(current_file)
        for _ in range(5):  # 最多向上查找5级
            if os.path.exists(os.path.join(search_path, 'run.py')) or \
               os.path.exists(os.path.join(search_path, 'src')) or \
               os.path.exists(os.path.join(search_path, 'requirements.txt')):
                root_path = search_path
                break
            parent = os.path.dirname(search_path)
            if parent == search_path:  # 已到根目录
                break
            search_path = parent

        return root_path

    @staticmethod
    def get_resource_path(resource_type, resource_name):
        """
        获取资源文件的绝对路径
        
        参数:
            resource_type: 资源类型 ('images', 'icons', 'stylesheets', etc.)
            resource_name: 资源文件名
            
        返回:
            资源文件的绝对路径
        """
        app_root = ResourceLoader.get_app_root_path()

        # 尝试多个可能的资源路径（增强版路径查找）
        possible_paths = [
            os.path.join(app_root, 'src', 'resources', resource_type, resource_name),
            os.path.join(app_root, 'resources', resource_type, resource_name),
            os.path.join(app_root, resource_type, resource_name),
            os.path.join(app_root, 'src', resource_type, resource_name)
        ]

        # 返回第一个存在的路径
        for path in possible_paths:
            if os.path.exists(path):
                return path

        # 如果都不存在，使用标准路径并尝试创建目录
        resource_path = possible_paths[0]  # 使用标准路径
        resource_dir = os.path.dirname(resource_path)

        if not os.path.exists(resource_dir):
            try:
                os.makedirs(resource_dir, exist_ok=True)
                logging.debug(f"创建资源目录: {resource_dir}")
            except Exception as e:
                logging.error(f"创建资源目录失败: {e}")

        # 检查文件是否存在
        if not os.path.exists(resource_path):
            # 只在第一次检查时输出警告，避免重复日志
            if resource_path not in ResourceLoader._missing_files_cache:
                logging.warning(f"资源文件不存在: {resource_path}")
                ResourceLoader._missing_files_cache.add(resource_path)

        return resource_path
    
    @staticmethod
    def load_pixmap(image_name):
        """
        加载图片资源
        
        参数:
            image_name: 图片文件名
            
        返回:
            QPixmap 对象
        """
        if image_name in ResourceLoader._pixmap_cache:
            return ResourceLoader._pixmap_cache[image_name]
            
        resource_path = ResourceLoader.get_resource_path('images', image_name)
        if os.path.exists(resource_path):
            try:
                pixmap = QPixmap(resource_path)
                if not pixmap.isNull():
                    ResourceLoader._pixmap_cache[image_name] = pixmap
                    return pixmap
            except Exception as e:
                logging.error(f"加载图片失败: {e}")
        
        # 只在第一次检查时输出警告，避免重复日志
        if resource_path not in ResourceLoader._missing_files_cache:
            logging.warning(f"图片文件不存在或无法加载: {resource_path}")
            ResourceLoader._missing_files_cache.add(resource_path)
        return QPixmap()
    
    @staticmethod
    def load_icon(icon_name):
        """
        加载图标资源
        
        参数:
            icon_name: 图标文件名
            
        返回:
            QIcon对象
        """
        # 检查缓存
        if icon_name in ResourceLoader._icon_cache:
            return ResourceLoader._icon_cache[icon_name]
        
        # 确保图标文件名以.png或.ico结尾
        if not (icon_name.endswith('.png') or icon_name.endswith('.ico') or icon_name.endswith('.jpg')):
            icon_name = f"{icon_name}.png"
        
        # 从图标目录加载
        resource_path = ResourceLoader.get_resource_path('icons', icon_name)
        
        # 如果图标不存在，尝试创建默认图标
        if not os.path.exists(resource_path):
            try:
                # 确保图标目录存在
                icons_dir = os.path.dirname(resource_path)
                if not os.path.exists(icons_dir):
                    os.makedirs(icons_dir, exist_ok=True)
                
                # 根据图标名称确定图标类型
                icon_type = ResourceLoader._get_icon_type_from_name(icon_name)
                
                if icon_type:
                    # 创建默认图标并保存
                    pixmap = ResourceLoader._create_default_icon_pixmap(icon_type)
                    pixmap.save(resource_path)
                    logging.info(f"已创建并保存默认图标: {resource_path}")
            except Exception as e:
                logging.error(f"创建默认图标失败: {icon_name}, 错误: {e}")
        
        if os.path.exists(resource_path):
            try:
                icon = QIcon(resource_path)
                ResourceLoader._icon_cache[icon_name] = icon
                return icon
            except Exception as e:
                logging.error(f"加载图标失败: {e}")
        
        # 如果找不到图标，尝试从images目录加载
        resource_path = ResourceLoader.get_resource_path('images', icon_name)
        if os.path.exists(resource_path):
            try:
                icon = QIcon(resource_path)
                ResourceLoader._icon_cache[icon_name] = icon
                logging.info(f"从images目录加载图标: {resource_path}")
                return icon
            except Exception as e:
                logging.error(f"从images目录加载图标失败: {e}")
        else:
            # 如果images目录也没有，尝试创建默认图标到images目录
            icon_type = ResourceLoader._get_icon_type_from_name(icon_name)
            if icon_type:
                try:
                    # 确保images目录存在
                    images_dir = os.path.dirname(resource_path)
                    if not os.path.exists(images_dir):
                        os.makedirs(images_dir, exist_ok=True)

                    # 创建默认图标并保存到images目录
                    pixmap = ResourceLoader._create_default_icon_pixmap(icon_type)
                    pixmap.save(resource_path)
                    logging.info(f"已创建并保存默认图标到images目录: {resource_path}")

                    # 加载刚创建的图标
                    icon = QIcon(resource_path)
                    ResourceLoader._icon_cache[icon_name] = icon
                    return icon
                except Exception as e:
                    logging.error(f"创建默认图标到images目录失败: {icon_name}, 错误: {e}")
        
        # 尝试直接创建默认图标
        icon_type = ResourceLoader._get_icon_type_from_name(icon_name)
        
        if icon_type:
            icon = ResourceLoader.create_default_icon(icon_type)
            ResourceLoader._icon_cache[icon_name] = icon
            return icon
        
        # 尝试从当前目录或上级目录查找
        app_root = ResourceLoader.get_app_root_path()
        alternative_paths = [
            os.path.join(app_root, 'icons', icon_name),
            os.path.join(app_root, 'resources', 'icons', icon_name),
            os.path.join(app_root, 'resources', 'images', icon_name),
            os.path.join(app_root, '..', 'icons', icon_name),
        ]
        
        for path in alternative_paths:
            if os.path.exists(path):
                try:
                    icon = QIcon(path)
                    ResourceLoader._icon_cache[icon_name] = icon
                    logging.info(f"从备用路径加载图标: {path}")
                    return icon
                except Exception as e:
                    logging.error(f"从备用路径加载图标失败: {path}, 错误: {e}")
        
        # 只在第一次检查时输出警告，避免重复日志
        if resource_path not in ResourceLoader._missing_files_cache:
            logging.warning(f"图标文件不存在或无法加载: {resource_path}")
            ResourceLoader._missing_files_cache.add(resource_path)
        return QIcon()
    
    @staticmethod
    def create_default_icon(icon_type):
        """创建默认图标"""
        pixmap = ResourceLoader._create_default_icon_pixmap(icon_type)
        return QIcon(pixmap)
    
    @staticmethod
    def load_stylesheet(stylesheet_name):
        """
        加载样式表文件
        
        参数:
            stylesheet_name: 样式表文件名
            
        返回:
            样式表内容字符串
        """
        if stylesheet_name in ResourceLoader._stylesheet_cache:
            return ResourceLoader._stylesheet_cache[stylesheet_name]
            
        resource_path = ResourceLoader.get_resource_path('stylesheets', stylesheet_name)
        
        # 尝试从默认样式表目录加载
        if os.path.exists(resource_path):
            try:
                with open(resource_path, 'r', encoding='utf-8', errors='ignore') as f:
                    stylesheet = f.read()
                    ResourceLoader._stylesheet_cache[stylesheet_name] = stylesheet
                    return stylesheet
            except UnicodeDecodeError:
                logging.error(f"样式表文件UTF-8解码失败，尝试使用不同编码: {resource_path}")
                try:
                    with open(resource_path, 'r', encoding='latin-1', errors='ignore') as f:
                        stylesheet = f.read()
                        ResourceLoader._stylesheet_cache[stylesheet_name] = stylesheet
                        return stylesheet
                except Exception as e:
                    logging.error(f"使用latin-1编码加载样式表失败: {e}")
            except Exception as e:
                logging.error(f"加载样式表失败: {e}")
        
        # 如果默认位置没有找到，尝试从styles目录加载（兼容旧版本）
        alt_path = ResourceLoader.get_resource_path('styles', stylesheet_name)
        if os.path.exists(alt_path):
            try:
                with open(alt_path, 'r', encoding='utf-8', errors='ignore') as f:
                    stylesheet = f.read()
                    ResourceLoader._stylesheet_cache[stylesheet_name] = stylesheet
                    return stylesheet
            except UnicodeDecodeError:
                logging.error(f"备用样式表文件UTF-8解码失败，尝试使用不同编码: {alt_path}")
                try:
                    with open(alt_path, 'r', encoding='latin-1', errors='ignore') as f:
                        stylesheet = f.read()
                        ResourceLoader._stylesheet_cache[stylesheet_name] = stylesheet
                        return stylesheet
                except Exception as e:
                    logging.error(f"使用latin-1编码加载备用样式表失败: {e}")
            except Exception as e:
                logging.error(f"从备用位置加载样式表失败: {e}")
        
        logging.warning(f"未找到样式表文件: {stylesheet_name}")
        return ""
    
    @staticmethod
    def clear_cache():
        """清除缓存的资源"""
        ResourceLoader._icon_cache.clear()
        ResourceLoader._pixmap_cache.clear()
        ResourceLoader._stylesheet_cache.clear()
        ResourceLoader._missing_files_cache.clear()
        logging.debug("资源缓存已清除")
    
    @staticmethod
    def create_resource_directories():
        """创建资源目录结构（如果不存在）"""
        app_root = ResourceLoader.get_app_root_path()
        resource_dir = os.path.join(app_root, 'src', 'resources')
        
        # 创建资源目录和子目录
        for subdir in ['images', 'icons', 'stylesheets']:
            dir_path = os.path.join(resource_dir, subdir)
            if not os.path.exists(dir_path):
                try:
                    os.makedirs(dir_path, exist_ok=True)
                    logging.info(f"已创建资源目录: {dir_path}")
                except Exception as e:
                    logging.error(f"创建资源目录失败: {dir_path}, 错误: {e}")
        
        # 确保快捷键图标目录存在
        icons_path = os.path.join(resource_dir, 'icons')
        ResourceLoader._create_default_shortcut_icons(icons_path)
        
        return True
        
    @staticmethod
    def _create_default_shortcut_icons(icons_dir):
        """创建默认的快捷键图标"""
        if not os.path.exists(icons_dir):
            try:
                os.makedirs(icons_dir, exist_ok=True)
            except Exception as e:
                logging.error(f"创建图标目录失败: {icons_dir}, 错误: {e}")
                return False
        
        # 定义需要创建的图标及其类型
        shortcut_icons = {
            "保存快捷键.png": "save",
            "另存为快捷键.png": "save_as",
            "撤销上一步操作快捷键.png": "undo",
            "恢复下一步操作快捷键.png": "redo",
            "查找快捷键.png": "search",
            "筛选快捷键.png": "filter",
            "合并快捷键.png": "save"  # 合并图标使用保存图标作为基础
        }
        
        for icon_name, icon_type in shortcut_icons.items():
            icon_path = os.path.join(icons_dir, icon_name)
            if not os.path.exists(icon_path):
                try:
                    # 创建默认图标并保存
                    pixmap = ResourceLoader._create_default_icon_pixmap(icon_type)
                    pixmap.save(icon_path)
                    logging.info(f"已创建默认图标: {icon_path}")
                except Exception as e:
                    logging.error(f"创建默认图标失败: {icon_path}, 错误: {e}")
        
        return True
        
    @staticmethod
    def _get_icon_type_from_name(icon_name):
        """根据图标名称确定图标类型"""
        icon_name_lower = icon_name.lower()

        # 中文图标名称映射
        if "保存快捷键" in icon_name_lower or "save" in icon_name_lower:
            return "save"
        elif "另存为快捷键" in icon_name_lower or "save_as" in icon_name_lower or "saveas" in icon_name_lower:
            return "save_as"
        elif "撤销上一步操作快捷键" in icon_name_lower or "undo" in icon_name_lower:
            return "undo"
        elif "恢复下一步操作快捷键" in icon_name_lower or "redo" in icon_name_lower:
            return "redo"
        elif "查找快捷键" in icon_name_lower or "search" in icon_name_lower or "find" in icon_name_lower:
            return "search"
        elif "筛选快捷键" in icon_name_lower or "filter" in icon_name_lower:
            return "filter"
        elif "合并快捷键" in icon_name_lower or "merge" in icon_name_lower:
            return "save"  # 合并图标使用保存图标作为基础

        # 英文基础图标名称映射
        elif "add" in icon_name_lower or "new" in icon_name_lower or "plus" in icon_name_lower:
            return "add"
        elif "delete" in icon_name_lower or "remove" in icon_name_lower or "trash" in icon_name_lower:
            return "delete"
        elif "refresh" in icon_name_lower or "reload" in icon_name_lower or "update" in icon_name_lower:
            return "refresh"
        elif "export" in icon_name_lower or "download" in icon_name_lower:
            return "export"
        elif "import" in icon_name_lower or "upload" in icon_name_lower:
            return "import"
        elif "edit" in icon_name_lower or "modify" in icon_name_lower:
            return "edit"
        elif "view" in icon_name_lower or "show" in icon_name_lower:
            return "view"
        elif "settings" in icon_name_lower or "config" in icon_name_lower or "gear" in icon_name_lower:
            return "settings"

        return None

    @staticmethod
    def _create_default_icon_pixmap(icon_type):
        """创建默认图标并返回QPixmap对象"""
        from PyQt5.QtGui import QPainter, QPixmap, QPen, QColor, QBrush
        from PyQt5.QtCore import Qt
        
        pixmap = QPixmap(24, 24)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        if icon_type == "save":
            # 绘制保存图标
            painter.setPen(QPen(QColor("#333333"), 2))
            painter.setBrush(QBrush(QColor("#f0f0f0")))
            painter.drawRect(4, 4, 16, 16)
            painter.drawRect(7, 2, 10, 4)
            painter.drawLine(7, 12, 17, 12)
            painter.drawLine(7, 16, 17, 16)
            
        elif icon_type == "save_as":
            # 绘制另存为图标
            painter.setPen(QPen(QColor("#333333"), 2))
            painter.setBrush(QBrush(QColor("#f0f0f0")))
            painter.drawRect(2, 2, 14, 14)
            painter.drawRect(5, 5, 16, 16)
            
        elif icon_type == "undo":
            # 绘制撤销图标
            painter.setPen(QPen(QColor("#333333"), 2))
            painter.setBrush(Qt.NoBrush)
            painter.drawArc(4, 4, 16, 16, 0, -270 * 16)
            painter.drawLine(4, 12, 8, 8)
            painter.drawLine(4, 12, 8, 16)
            
        elif icon_type == "redo":
            # 绘制重做图标
            painter.setPen(QPen(QColor("#333333"), 2))
            painter.setBrush(Qt.NoBrush)
            painter.drawArc(4, 4, 16, 16, 0, 270 * 16)
            painter.drawLine(20, 12, 16, 8)
            painter.drawLine(20, 12, 16, 16)
            
        elif icon_type == "search":
            # 绘制搜索图标
            painter.setPen(QPen(QColor("#333333"), 2))
            painter.setBrush(Qt.NoBrush)
            painter.drawEllipse(4, 4, 12, 12)
            painter.drawLine(16, 16, 20, 20)
            
        elif icon_type == "filter":
            # 绘制筛选图标
            painter.setPen(QPen(QColor("#333333"), 2))
            painter.setBrush(Qt.NoBrush)
            painter.drawLine(4, 6, 20, 6)
            painter.drawLine(6, 12, 18, 12)
            painter.drawLine(8, 18, 16, 18)

        elif icon_type == "add":
            # 绘制添加图标（加号）
            painter.setPen(QPen(QColor("#2e7d32"), 3))
            painter.setBrush(Qt.NoBrush)
            painter.drawLine(12, 6, 12, 18)  # 垂直线
            painter.drawLine(6, 12, 18, 12)  # 水平线

        elif icon_type == "delete":
            # 绘制删除图标（垃圾桶）
            painter.setPen(QPen(QColor("#d32f2f"), 2))
            painter.setBrush(QBrush(QColor("#ffebee")))
            # 垃圾桶主体
            painter.drawRect(7, 8, 10, 12)
            # 垃圾桶盖子
            painter.drawRect(6, 6, 12, 2)
            # 垃圾桶把手
            painter.drawRect(9, 4, 6, 2)
            # 垃圾桶内部线条
            painter.drawLine(9, 10, 9, 18)
            painter.drawLine(12, 10, 12, 18)
            painter.drawLine(15, 10, 15, 18)

        elif icon_type == "refresh":
            # 绘制刷新图标（圆形箭头）
            painter.setPen(QPen(QColor("#1976d2"), 2))
            painter.setBrush(Qt.NoBrush)
            # 绘制圆弧
            painter.drawArc(4, 4, 16, 16, 30 * 16, 300 * 16)
            # 绘制箭头
            painter.drawLine(18, 6, 20, 4)
            painter.drawLine(18, 6, 20, 8)

        elif icon_type == "export":
            # 绘制导出图标（向上箭头+底线）
            painter.setPen(QPen(QColor("#388e3c"), 2))
            painter.setBrush(Qt.NoBrush)
            # 向上箭头
            painter.drawLine(12, 4, 12, 16)  # 箭头主体
            painter.drawLine(12, 4, 8, 8)    # 左箭头
            painter.drawLine(12, 4, 16, 8)   # 右箭头
            # 底线
            painter.drawLine(6, 20, 18, 20)

        elif icon_type == "import":
            # 绘制导入图标（向下箭头+底线）
            painter.setPen(QPen(QColor("#f57c00"), 2))
            painter.setBrush(Qt.NoBrush)
            # 向下箭头
            painter.drawLine(12, 4, 12, 16)  # 箭头主体
            painter.drawLine(12, 16, 8, 12)  # 左箭头
            painter.drawLine(12, 16, 16, 12) # 右箭头
            # 底线
            painter.drawLine(6, 20, 18, 20)

        elif icon_type == "edit":
            # 绘制编辑图标（铅笔）
            painter.setPen(QPen(QColor("#795548"), 2))
            painter.setBrush(Qt.NoBrush)
            # 铅笔主体
            painter.drawLine(6, 18, 18, 6)
            painter.drawLine(4, 20, 6, 18)
            painter.drawRect(16, 4, 4, 4)

        elif icon_type == "view":
            # 绘制查看图标（眼睛）
            painter.setPen(QPen(QColor("#424242"), 2))
            painter.setBrush(Qt.NoBrush)
            # 眼睛轮廓
            painter.drawEllipse(4, 8, 16, 8)
            # 瞳孔
            painter.setBrush(QBrush(QColor("#424242")))
            painter.drawEllipse(10, 10, 4, 4)

        elif icon_type == "settings":
            # 绘制设置图标（齿轮）
            painter.setPen(QPen(QColor("#616161"), 2))
            painter.setBrush(Qt.NoBrush)
            # 齿轮外圈
            painter.drawEllipse(6, 6, 12, 12)
            # 齿轮内圈
            painter.drawEllipse(9, 9, 6, 6)
            # 齿轮齿
            for i in range(8):
                angle = i * 45
                import math
                x1 = 12 + 8 * math.cos(math.radians(angle))
                y1 = 12 + 8 * math.sin(math.radians(angle))
                x2 = 12 + 10 * math.cos(math.radians(angle))
                y2 = 12 + 10 * math.sin(math.radians(angle))
                painter.drawLine(int(x1), int(y1), int(x2), int(y2))

        painter.end()
        return pixmap