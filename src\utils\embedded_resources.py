"""
嵌入式资源模块

该模块将JavaScript资源直接嵌入到Python代码中，
避免WebEngine访问本地文件系统的权限问题。

作者: AI Assistant
创建时间: 2025-08-05
"""

import os
import logging
from pathlib import Path

logger = logging.getLogger(__name__)


class EmbeddedResources:
    """嵌入式资源管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._echarts_js = None
        self._wordcloud_js = None
        self._resources_loaded = False
    
    def load_resources(self) -> bool:
        """
        加载JavaScript资源到内存
        
        返回:
            bool: 加载是否成功
        """
        try:
            # 获取资源文件路径
            current_dir = Path(__file__).parent
            project_root = current_dir.parent.parent
            js_dir = project_root / "src" / "resources" / "js"
            
            echarts_file = js_dir / "echarts.min.js"
            wordcloud_file = js_dir / "echarts-wordcloud.min.js"
            
            # 读取ECharts主库
            if echarts_file.exists():
                with open(echarts_file, 'r', encoding='utf-8') as f:
                    self._echarts_js = f.read()
                self.logger.info(f"ECharts主库已加载到内存 ({len(self._echarts_js)} 字符)")
            else:
                self.logger.error(f"ECharts主库文件不存在: {echarts_file}")
                return False
            
            # 读取词云插件
            if wordcloud_file.exists():
                with open(wordcloud_file, 'r', encoding='utf-8') as f:
                    self._wordcloud_js = f.read()
                self.logger.info(f"词云插件已加载到内存 ({len(self._wordcloud_js)} 字符)")
            else:
                self.logger.error(f"词云插件文件不存在: {wordcloud_file}")
                return False
            
            self._resources_loaded = True
            self.logger.info("所有JavaScript资源已成功加载到内存")
            return True
            
        except Exception as e:
            self.logger.error(f"加载JavaScript资源失败: {e}")
            return False
    
    def get_echarts_js(self) -> str:
        """
        获取ECharts JavaScript代码
        
        返回:
            str: ECharts JavaScript代码
        """
        if not self._resources_loaded:
            self.load_resources()
        
        return self._echarts_js or ""
    
    def get_wordcloud_js(self) -> str:
        """
        获取词云插件JavaScript代码
        
        返回:
            str: 词云插件JavaScript代码
        """
        if not self._resources_loaded:
            self.load_resources()
        
        return self._wordcloud_js or ""
    
    def get_embedded_html_scripts(self) -> str:
        """
        获取嵌入式HTML脚本标签
        
        返回:
            str: 包含所有JavaScript代码的HTML脚本标签
        """
        if not self._resources_loaded:
            if not self.load_resources():
                # 如果加载失败，返回在线版本作为回退
                return '''
                <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
                <script src="https://assets.pyecharts.org/assets/v5/echarts-wordcloud.min.js"></script>
                '''
        
        # 构建嵌入式脚本
        embedded_html = f'''
        <!-- 嵌入式ECharts资源 - 完全离线 -->
        <script type="text/javascript">
        // ECharts主库 (嵌入式)
        {self._echarts_js}
        </script>
        
        <script type="text/javascript">
        // ECharts词云插件 (嵌入式)
        {self._wordcloud_js}
        </script>
        '''
        
        return embedded_html
    
    def is_resources_available(self) -> bool:
        """
        检查资源是否可用
        
        返回:
            bool: 资源是否可用
        """
        if not self._resources_loaded:
            self.load_resources()
        
        return (self._echarts_js is not None and 
                self._wordcloud_js is not None and 
                len(self._echarts_js) > 0 and 
                len(self._wordcloud_js) > 0)
    
    def get_resource_info(self) -> dict:
        """
        获取资源信息
        
        返回:
            dict: 资源信息
        """
        if not self._resources_loaded:
            self.load_resources()
        
        return {
            "echarts_loaded": self._echarts_js is not None,
            "wordcloud_loaded": self._wordcloud_js is not None,
            "echarts_size": len(self._echarts_js) if self._echarts_js else 0,
            "wordcloud_size": len(self._wordcloud_js) if self._wordcloud_js else 0,
            "total_size": (len(self._echarts_js) if self._echarts_js else 0) + 
                         (len(self._wordcloud_js) if self._wordcloud_js else 0),
            "resources_loaded": self._resources_loaded
        }


# 全局实例
embedded_resources = EmbeddedResources()


def get_embedded_chart_scripts() -> str:
    """
    获取嵌入式图表脚本的便捷函数
    
    返回:
        str: 嵌入式HTML脚本标签
    """
    return embedded_resources.get_embedded_html_scripts()


def check_embedded_resources() -> dict:
    """
    检查嵌入式资源状态的便捷函数
    
    返回:
        dict: 资源状态信息
    """
    return embedded_resources.get_resource_info()


def is_embedded_resources_ready() -> bool:
    """
    检查嵌入式资源是否就绪的便捷函数
    
    返回:
        bool: 资源是否就绪
    """
    return embedded_resources.is_resources_available()
