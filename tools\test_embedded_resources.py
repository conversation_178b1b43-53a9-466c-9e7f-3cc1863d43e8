#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
嵌入式资源测试工具

该工具用于测试嵌入式JavaScript资源的加载和使用。

使用方法:
    python tools/test_embedded_resources.py

作者: AI Assistant
创建时间: 2025-08-05
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_embedded_resources():
    """测试嵌入式资源"""
    print("="*80)
    print("嵌入式资源测试")
    print("="*80)
    
    try:
        # 导入嵌入式资源模块
        from src.utils.embedded_resources import (
            embedded_resources, 
            get_embedded_chart_scripts, 
            check_embedded_resources,
            is_embedded_resources_ready
        )
        
        print("✅ 嵌入式资源模块导入成功")
        
        # 检查资源状态
        print("\n📊 检查资源状态...")
        resource_info = check_embedded_resources()
        
        print(f"ECharts主库加载: {'✅' if resource_info['echarts_loaded'] else '❌'}")
        print(f"词云插件加载: {'✅' if resource_info['wordcloud_loaded'] else '❌'}")
        print(f"ECharts大小: {resource_info['echarts_size']:,} 字符")
        print(f"词云插件大小: {resource_info['wordcloud_size']:,} 字符")
        print(f"总大小: {resource_info['total_size']:,} 字符")
        print(f"资源就绪: {'✅' if resource_info['resources_loaded'] else '❌'}")
        
        # 测试资源就绪状态
        print(f"\n🔍 资源就绪检查: {'✅' if is_embedded_resources_ready() else '❌'}")
        
        # 获取嵌入式脚本
        print("\n📝 生成嵌入式HTML脚本...")
        embedded_scripts = get_embedded_chart_scripts()
        
        if embedded_scripts:
            script_length = len(embedded_scripts)
            print(f"✅ 嵌入式脚本生成成功 ({script_length:,} 字符)")
            
            # 检查脚本内容
            if "echarts" in embedded_scripts.lower():
                print("✅ 脚本包含ECharts代码")
            else:
                print("❌ 脚本不包含ECharts代码")
                
            if "wordcloud" in embedded_scripts.lower():
                print("✅ 脚本包含词云插件代码")
            else:
                print("❌ 脚本不包含词云插件代码")
                
            # 保存测试HTML文件
            test_html_path = project_root / "test_embedded_chart.html"
            test_html = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>嵌入式ECharts测试</title>
    {embedded_scripts}
</head>
<body>
    <div id="test-chart" style="width: 600px; height: 400px; margin: 20px auto;"></div>
    
    <script>
        // 测试ECharts是否可用
        if (typeof echarts !== 'undefined') {{
            console.log('ECharts加载成功');
            
            // 创建测试图表
            var chart = echarts.init(document.getElementById('test-chart'));
            var option = {{
                title: {{
                    text: '嵌入式ECharts测试'
                }},
                tooltip: {{}},
                xAxis: {{
                    data: ['A', 'B', 'C', 'D']
                }},
                yAxis: {{}},
                series: [{{
                    name: '测试数据',
                    type: 'bar',
                    data: [10, 20, 15, 25]
                }}]
            }};
            chart.setOption(option);
            
            document.body.innerHTML += '<p style="text-align: center; color: green;">✅ ECharts嵌入式资源测试成功！</p>';
        }} else {{
            console.error('ECharts未加载');
            document.body.innerHTML += '<p style="text-align: center; color: red;">❌ ECharts嵌入式资源测试失败！</p>';
        }}
    </script>
</body>
</html>
"""
            
            with open(test_html_path, 'w', encoding='utf-8') as f:
                f.write(test_html)
            
            print(f"📄 测试HTML文件已生成: {test_html_path}")
            print("💡 可以在浏览器中打开此文件测试嵌入式资源")
            
        else:
            print("❌ 嵌入式脚本生成失败")
            
    except ImportError as e:
        print(f"❌ 导入嵌入式资源模块失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False
    
    print("\n" + "="*80)
    return True


def test_chart_renderer_integration():
    """测试图表渲染器集成"""
    print("\n📊 测试图表渲染器集成...")
    
    try:
        from src.utils.chart_renderer import ChartRenderer
        
        # 创建图表渲染器实例
        renderer = ChartRenderer()
        
        print(f"嵌入式模式启用: {'✅' if renderer.embedded_mode_enabled else '❌'}")
        print(f"传统离线模式启用: {'✅' if renderer.offline_mode_enabled else '❌'}")
        
        # 测试简单图表渲染
        import pandas as pd
        test_data = pd.DataFrame({
            'category': ['A', 'B', 'C', 'D'],
            'value': [10, 20, 15, 25]
        })
        
        config = {
            'title': '嵌入式资源测试图表',
            'width': 600,
            'height': 400,
            'chart_type': 'bar',
            'x_field': 'category',
            'y_field': 'value'
        }

        print("🔧 测试柱状图渲染...")
        html_result = renderer.render_chart(config, test_data)
        
        if html_result and len(html_result) > 0:
            print(f"✅ 柱状图渲染成功 ({len(html_result):,} 字符)")
            
            # 保存测试结果
            test_chart_path = project_root / "test_embedded_bar_chart.html"
            with open(test_chart_path, 'w', encoding='utf-8') as f:
                f.write(html_result)
            print(f"📄 测试图表已保存: {test_chart_path}")
            
        else:
            print("❌ 柱状图渲染失败")
            
    except Exception as e:
        print(f"❌ 图表渲染器集成测试失败: {e}")
        return False
    
    return True


def main():
    """主函数"""
    print("智能驾驶试验管控工具 - 嵌入式资源测试")
    
    success = True
    
    # 测试嵌入式资源
    if not test_embedded_resources():
        success = False
    
    # 测试图表渲染器集成
    if not test_chart_renderer_integration():
        success = False
    
    print("\n" + "="*80)
    if success:
        print("🎉 所有测试通过！嵌入式资源功能正常。")
        print("💡 现在可以在完全离线环境下使用图表功能。")
    else:
        print("❌ 部分测试失败，请检查错误信息。")
    
    print("="*80)


if __name__ == "__main__":
    main()
