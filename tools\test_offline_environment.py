#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
离线环境测试工具

该工具用于全面测试离线环境的完整性和资源可用性，
帮助诊断和解决离线部署中的问题。

作者: AI Assistant
创建时间: 2025-08-05
"""

import os
import sys
import json
import logging
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class OfflineEnvironmentTester:
    """离线环境测试器"""
    
    def __init__(self):
        self.project_root = project_root
        self.logger = self._setup_logger()
        self.test_results = {}
        self.issues_found = []
        self.recommendations = []
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 创建格式器
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        
        logger.addHandler(console_handler)
        return logger
    
    def test_python_environment(self) -> Dict[str, Any]:
        """测试Python环境"""
        self.logger.info("🐍 测试Python环境...")
        
        result = {
            "python_version": sys.version,
            "python_executable": sys.executable,
            "virtual_env": None,
            "pip_available": False,
            "status": "unknown"
        }
        
        try:
            # 检查虚拟环境
            if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
                result["virtual_env"] = sys.prefix
                self.logger.info(f"✅ 虚拟环境: {sys.prefix}")
            else:
                self.logger.warning("⚠️ 未检测到虚拟环境")
                self.issues_found.append("未使用虚拟环境")
            
            # 检查pip
            try:
                import pip
                result["pip_available"] = True
                self.logger.info("✅ pip可用")
            except ImportError:
                self.logger.warning("⚠️ pip不可用")
                self.issues_found.append("pip不可用")
            
            result["status"] = "ok"
            
        except Exception as e:
            self.logger.error(f"❌ Python环境测试失败: {e}")
            result["status"] = "error"
            self.issues_found.append(f"Python环境异常: {e}")
        
        return result
    
    def test_dependencies(self) -> Dict[str, Any]:
        """测试依赖包"""
        self.logger.info("📦 测试依赖包...")
        
        critical_packages = [
            "PyQt5", "pandas", "numpy", "matplotlib", 
            "pyecharts", "openpyxl", "xlrd", "PIL", "pyqtgraph"
        ]
        
        result = {
            "installed_packages": {},
            "missing_packages": [],
            "status": "unknown"
        }
        
        for package in critical_packages:
            try:
                if package == "PIL":
                    import PIL
                    result["installed_packages"][package] = PIL.__version__
                else:
                    module = __import__(package)
                    version = getattr(module, '__version__', 'unknown')
                    result["installed_packages"][package] = version
                
                self.logger.info(f"✅ {package}: {version}")
                
            except ImportError:
                result["missing_packages"].append(package)
                self.logger.warning(f"❌ {package}: 未安装")
                self.issues_found.append(f"缺少依赖包: {package}")
        
        if result["missing_packages"]:
            result["status"] = "warning"
            self.recommendations.append("安装缺失的依赖包: pip install -r requirements.txt")
        else:
            result["status"] = "ok"
        
        return result
    
    def test_resource_files(self) -> Dict[str, Any]:
        """测试资源文件"""
        self.logger.info("📁 测试资源文件...")
        
        result = {
            "icons": {"exists": False, "count": 0, "missing": []},
            "javascript": {"exists": False, "files": {}},
            "stylesheets": {"exists": False, "count": 0},
            "status": "unknown"
        }
        
        # 测试图标资源
        icons_dir = self.project_root / "src" / "resources" / "icons"
        if icons_dir.exists():
            result["icons"]["exists"] = True
            icon_files = list(icons_dir.glob("*.png")) + list(icons_dir.glob("*.ico"))
            result["icons"]["count"] = len(icon_files)
            
            # 检查关键图标
            critical_icons = ["add.png", "delete.png", "refresh.png", "logo.ico"]
            for icon in critical_icons:
                if not (icons_dir / icon).exists():
                    result["icons"]["missing"].append(icon)
            
            self.logger.info(f"✅ 图标资源: {result['icons']['count']} 个文件")
        else:
            self.logger.warning("❌ 图标资源目录不存在")
            self.issues_found.append("图标资源目录不存在")
        
        # 测试JavaScript资源
        js_dir = self.project_root / "src" / "resources" / "js"
        if js_dir.exists():
            result["javascript"]["exists"] = True
            
            echarts_file = js_dir / "echarts.min.js"
            wordcloud_file = js_dir / "echarts-wordcloud.min.js"
            
            if echarts_file.exists():
                result["javascript"]["files"]["echarts"] = echarts_file.stat().st_size
                self.logger.info("✅ ECharts主库存在")
            else:
                self.logger.warning("❌ ECharts主库不存在")
                self.issues_found.append("ECharts主库不存在")
            
            if wordcloud_file.exists():
                result["javascript"]["files"]["wordcloud"] = wordcloud_file.stat().st_size
                self.logger.info("✅ 词云插件存在")
            else:
                self.logger.warning("❌ 词云插件不存在")
                self.issues_found.append("词云插件不存在")
        else:
            self.logger.warning("❌ JavaScript资源目录不存在")
            self.issues_found.append("JavaScript资源目录不存在")
        
        # 测试样式表资源
        css_dir = self.project_root / "src" / "resources" / "stylesheets"
        if css_dir.exists():
            result["stylesheets"]["exists"] = True
            css_files = list(css_dir.glob("*.qss")) + list(css_dir.glob("*.css"))
            result["stylesheets"]["count"] = len(css_files)
            self.logger.info(f"✅ 样式表资源: {result['stylesheets']['count']} 个文件")
        else:
            self.logger.warning("❌ 样式表资源目录不存在")
            self.issues_found.append("样式表资源目录不存在")
        
        # 确定整体状态
        if result["icons"]["exists"] and result["javascript"]["exists"]:
            if result["icons"]["missing"] or not result["javascript"]["files"]:
                result["status"] = "warning"
            else:
                result["status"] = "ok"
        else:
            result["status"] = "error"
        
        return result
    
    def test_database_access(self) -> Dict[str, Any]:
        """测试数据库访问"""
        self.logger.info("🗄️ 测试数据库访问...")
        
        result = {
            "sqlite_available": False,
            "database_files": {},
            "permissions": {},
            "status": "unknown"
        }
        
        try:
            # 测试SQLite
            import sqlite3
            conn = sqlite3.connect(':memory:')
            conn.close()
            result["sqlite_available"] = True
            self.logger.info("✅ SQLite可用")
            
            # 检查数据库文件
            db_files = [
                self.project_root / "智能驾驶试验管控数据库.db",
                self.project_root / "app" / "tms_data.db",
                self.project_root / "data" / "test_management.db"
            ]
            
            for db_file in db_files:
                if db_file.exists():
                    result["database_files"][db_file.name] = {
                        "exists": True,
                        "size": db_file.stat().st_size,
                        "readable": os.access(db_file, os.R_OK),
                        "writable": os.access(db_file, os.W_OK)
                    }
                    
                    if not os.access(db_file, os.W_OK):
                        self.issues_found.append(f"数据库文件无写入权限: {db_file.name}")
                        self.recommendations.append(f"修复数据库权限: attrib -R \"{db_file}\"")
                    
                    self.logger.info(f"✅ 数据库文件: {db_file.name}")
                else:
                    result["database_files"][db_file.name] = {"exists": False}
                    self.logger.info(f"ℹ️ 数据库文件不存在: {db_file.name} (运行时创建)")
            
            result["status"] = "ok"
            
        except Exception as e:
            self.logger.error(f"❌ 数据库测试失败: {e}")
            result["status"] = "error"
            self.issues_found.append(f"数据库访问异常: {e}")
        
        return result
    
    def test_webengine_availability(self) -> Dict[str, Any]:
        """测试WebEngine可用性"""
        self.logger.info("🌐 测试WebEngine可用性...")
        
        result = {
            "webengine_available": False,
            "webengine_version": None,
            "qt_version": None,
            "status": "unknown"
        }
        
        try:
            # 测试PyQt5
            from PyQt5.QtCore import QT_VERSION_STR
            result["qt_version"] = QT_VERSION_STR
            self.logger.info(f"✅ Qt版本: {QT_VERSION_STR}")
            
            # 测试WebEngine
            try:
                from PyQt5.QtWebEngineWidgets import QWebEngineView
                result["webengine_available"] = True
                self.logger.info("✅ WebEngine可用")
                result["status"] = "ok"
            except ImportError as e:
                self.logger.warning(f"⚠️ WebEngine不可用: {e}")
                self.issues_found.append("WebEngine不可用")
                self.recommendations.append("安装WebEngine: pip install PyQtWebEngine")
                result["status"] = "warning"
                
        except Exception as e:
            self.logger.error(f"❌ WebEngine测试失败: {e}")
            result["status"] = "error"
            self.issues_found.append(f"WebEngine测试异常: {e}")
        
        return result
    
    def test_offline_configuration(self) -> Dict[str, Any]:
        """测试离线配置"""
        self.logger.info("⚙️ 测试离线配置...")
        
        result = {
            "offline_chart_config": False,
            "embedded_resources": False,
            "webengine_optimizer": False,
            "status": "unknown"
        }
        
        try:
            # 测试离线图表配置
            try:
                from src.utils.offline_chart_config import setup_offline_charts
                result["offline_chart_config"] = setup_offline_charts()
                self.logger.info("✅ 离线图表配置可用")
            except ImportError:
                self.logger.warning("⚠️ 离线图表配置模块不可用")
                self.issues_found.append("离线图表配置模块不可用")
            
            # 测试嵌入式资源
            try:
                from src.utils.embedded_resources import is_embedded_resources_ready
                result["embedded_resources"] = is_embedded_resources_ready()
                if result["embedded_resources"]:
                    self.logger.info("✅ 嵌入式资源就绪")
                else:
                    self.logger.warning("⚠️ 嵌入式资源未就绪")
            except ImportError:
                self.logger.warning("⚠️ 嵌入式资源模块不可用")
                self.issues_found.append("嵌入式资源模块不可用")
            
            # 测试WebEngine优化器
            try:
                from src.utils.webengine_offline_optimizer import webengine_optimizer
                result["webengine_optimizer"] = True
                self.logger.info("✅ WebEngine优化器可用")
            except ImportError:
                self.logger.warning("⚠️ WebEngine优化器不可用")
                self.issues_found.append("WebEngine优化器不可用")
            
            # 确定整体状态
            if any(result.values()):
                result["status"] = "ok" if all(result.values()) else "warning"
            else:
                result["status"] = "error"
                
        except Exception as e:
            self.logger.error(f"❌ 离线配置测试失败: {e}")
            result["status"] = "error"
            self.issues_found.append(f"离线配置测试异常: {e}")
        
        return result
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """运行综合测试"""
        self.logger.info("🚀 开始离线环境综合测试...")
        
        # 运行所有测试
        self.test_results = {
            "python_environment": self.test_python_environment(),
            "dependencies": self.test_dependencies(),
            "resource_files": self.test_resource_files(),
            "database_access": self.test_database_access(),
            "webengine_availability": self.test_webengine_availability(),
            "offline_configuration": self.test_offline_configuration()
        }
        
        # 计算整体状态
        statuses = [result.get("status", "unknown") for result in self.test_results.values()]
        if "error" in statuses:
            overall_status = "error"
        elif "warning" in statuses:
            overall_status = "warning"
        else:
            overall_status = "ok"
        
        self.test_results["overall_status"] = overall_status
        self.test_results["issues_count"] = len(self.issues_found)
        self.test_results["recommendations_count"] = len(self.recommendations)
        
        return self.test_results
    
    def generate_report(self) -> str:
        """生成测试报告"""
        report = []
        report.append("=" * 70)
        report.append("离线环境测试报告")
        report.append("=" * 70)
        report.append(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"项目路径: {self.project_root}")
        report.append(f"整体状态: {self.test_results.get('overall_status', 'unknown')}")
        report.append("")
        
        # 测试结果摘要
        report.append("测试结果摘要:")
        for test_name, result in self.test_results.items():
            if test_name not in ["overall_status", "issues_count", "recommendations_count"]:
                status = result.get("status", "unknown")
                status_icon = {"ok": "✅", "warning": "⚠️", "error": "❌"}.get(status, "❓")
                report.append(f"  {status_icon} {test_name}: {status}")
        report.append("")
        
        # 发现的问题
        if self.issues_found:
            report.append("发现的问题:")
            for i, issue in enumerate(self.issues_found, 1):
                report.append(f"  {i}. {issue}")
            report.append("")
        
        # 建议
        if self.recommendations:
            report.append("修复建议:")
            for i, rec in enumerate(self.recommendations, 1):
                report.append(f"  {i}. {rec}")
            report.append("")
        
        # 通用建议
        report.append("通用建议:")
        report.append("1. 使用 run1_fixed.bat 启动应用程序")
        report.append("2. 运行 python tools/fix_offline_resources.py 修复资源问题")
        report.append("3. 确保项目目录具有完整的读写权限")
        report.append("4. 如果问题持续，检查防病毒软件设置")
        report.append("")
        report.append("=" * 70)
        
        return "\n".join(report)


def main():
    """主函数"""
    print("🚀 离线环境测试工具")
    print("=" * 50)
    
    tester = OfflineEnvironmentTester()
    
    # 运行综合测试
    print("\n📋 运行综合测试...")
    results = tester.run_comprehensive_test()
    
    # 显示结果
    print(f"\n📊 测试完成:")
    print(f"  整体状态: {results['overall_status']}")
    print(f"  发现问题: {results['issues_count']} 个")
    print(f"  修复建议: {results['recommendations_count']} 条")
    
    # 生成报告
    print(f"\n📄 生成测试报告...")
    report = tester.generate_report()
    print(report)
    
    # 保存报告到文件
    report_file = tester.project_root / "temp" / "offline_environment_test_report.txt"
    report_file.parent.mkdir(exist_ok=True)
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    print(f"📄 报告已保存到: {report_file}")
    
    # 保存详细结果到JSON
    json_file = tester.project_root / "temp" / "offline_test_results.json"
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False, default=str)
    print(f"📄 详细结果已保存到: {json_file}")
    
    print("\n" + "=" * 50)
    if results['overall_status'] == 'ok':
        print("✅ 离线环境测试通过！")
    elif results['overall_status'] == 'warning':
        print("⚠️ 离线环境存在一些问题，但基本可用。")
    else:
        print("❌ 离线环境存在严重问题，需要修复。")


if __name__ == "__main__":
    main()
