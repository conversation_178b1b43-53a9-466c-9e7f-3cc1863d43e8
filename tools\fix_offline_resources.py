#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
离线资源修复工具

该工具用于修复离线环境下的资源加载问题，包括：
1. 图标资源路径修复
2. WebEngine离线配置优化
3. JavaScript资源嵌入式配置
4. 资源完整性验证

作者: AI Assistant
创建时间: 2025-08-05
"""

import os
import sys
import json
import logging
import shutil
from pathlib import Path
from typing import Dict, List, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.utils.resource_loader import ResourceLoader
    from src.utils.offline_chart_config import OfflineChartConfig
    from src.utils.embedded_resources import EmbeddedResources
except ImportError as e:
    print(f"警告: 无法导入项目模块: {e}")
    ResourceLoader = None
    OfflineChartConfig = None
    EmbeddedResources = None

class OfflineResourceFixer:
    """离线资源修复器"""
    
    def __init__(self):
        self.project_root = project_root
        self.logger = self._setup_logger()
        self.issues_found = []
        self.fixes_applied = []
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 创建格式器
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        
        logger.addHandler(console_handler)
        return logger
    
    def check_icon_resources(self) -> Dict[str, any]:
        """检查图标资源完整性"""
        self.logger.info("🔍 检查图标资源完整性...")
        
        icons_dir = self.project_root / "src" / "resources" / "icons"
        result = {
            "icons_dir_exists": icons_dir.exists(),
            "missing_icons": [],
            "total_icons": 0,
            "status": "unknown"
        }
        
        if not icons_dir.exists():
            self.issues_found.append("图标目录不存在")
            result["status"] = "error"
            return result
        
        # 检查关键图标文件
        critical_icons = [
            "add.png", "delete.png", "edit.png", "refresh.png", 
            "import.png", "export.png", "view.png", "settings.png",
            "logo.ico", "empty.png"
        ]
        
        for icon_name in critical_icons:
            icon_path = icons_dir / icon_name
            if not icon_path.exists():
                result["missing_icons"].append(icon_name)
                self.issues_found.append(f"缺少关键图标: {icon_name}")
        
        # 统计总图标数量
        result["total_icons"] = len(list(icons_dir.glob("*.png"))) + len(list(icons_dir.glob("*.ico")))
        
        if result["missing_icons"]:
            result["status"] = "warning"
            self.logger.warning(f"发现 {len(result['missing_icons'])} 个缺失的关键图标")
        else:
            result["status"] = "ok"
            self.logger.info("✅ 图标资源检查通过")
        
        return result
    
    def check_javascript_resources(self) -> Dict[str, any]:
        """检查JavaScript资源完整性"""
        self.logger.info("🔍 检查JavaScript资源完整性...")
        
        js_dir = self.project_root / "src" / "resources" / "js"
        result = {
            "js_dir_exists": js_dir.exists(),
            "echarts_exists": False,
            "wordcloud_exists": False,
            "echarts_size": 0,
            "wordcloud_size": 0,
            "status": "unknown"
        }
        
        if not js_dir.exists():
            self.issues_found.append("JavaScript资源目录不存在")
            result["status"] = "error"
            return result
        
        # 检查ECharts主库
        echarts_file = js_dir / "echarts.min.js"
        if echarts_file.exists():
            result["echarts_exists"] = True
            result["echarts_size"] = echarts_file.stat().st_size
        else:
            self.issues_found.append("ECharts主库文件不存在")
        
        # 检查词云插件
        wordcloud_file = js_dir / "echarts-wordcloud.min.js"
        if wordcloud_file.exists():
            result["wordcloud_exists"] = True
            result["wordcloud_size"] = wordcloud_file.stat().st_size
        else:
            self.issues_found.append("ECharts词云插件文件不存在")
        
        if result["echarts_exists"] and result["wordcloud_exists"]:
            result["status"] = "ok"
            self.logger.info("✅ JavaScript资源检查通过")
        elif result["echarts_exists"] or result["wordcloud_exists"]:
            result["status"] = "warning"
            self.logger.warning("⚠️ JavaScript资源不完整")
        else:
            result["status"] = "error"
            self.logger.error("❌ JavaScript资源缺失")
        
        return result
    
    def create_missing_icons(self) -> bool:
        """创建缺失的图标"""
        self.logger.info("🔧 创建缺失的图标...")
        
        try:
            if ResourceLoader is None:
                self.logger.error("无法导入ResourceLoader，跳过图标创建")
                return False
            
            icons_dir = self.project_root / "src" / "resources" / "icons"
            icons_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建基本图标
            basic_icons = {
                "add.png": "add",
                "delete.png": "delete", 
                "edit.png": "edit",
                "refresh.png": "refresh",
                "import.png": "import",
                "export.png": "export",
                "view.png": "view",
                "settings.png": "settings"
            }
            
            created_count = 0
            for icon_name, icon_type in basic_icons.items():
                icon_path = icons_dir / icon_name
                if not icon_path.exists():
                    try:
                        # 使用ResourceLoader创建默认图标
                        icon = ResourceLoader.create_default_icon(icon_type)
                        if not icon.isNull():
                            # 保存图标
                            pixmap = icon.pixmap(32, 32)
                            pixmap.save(str(icon_path))
                            created_count += 1
                            self.fixes_applied.append(f"创建图标: {icon_name}")
                    except Exception as e:
                        self.logger.error(f"创建图标失败 {icon_name}: {e}")
            
            if created_count > 0:
                self.logger.info(f"✅ 成功创建 {created_count} 个图标")
                return True
            else:
                self.logger.info("ℹ️ 无需创建图标")
                return True
                
        except Exception as e:
            self.logger.error(f"创建图标过程失败: {e}")
            return False
    
    def setup_embedded_resources(self) -> bool:
        """设置嵌入式资源模式"""
        self.logger.info("🔧 设置嵌入式资源模式...")
        
        try:
            if EmbeddedResources is None:
                self.logger.error("无法导入EmbeddedResources，跳过嵌入式资源设置")
                return False
            
            # 创建嵌入式资源实例
            embedded_resources = EmbeddedResources()
            
            # 尝试加载资源
            if embedded_resources.load_resources():
                self.logger.info("✅ 嵌入式资源加载成功")
                
                # 获取资源信息
                info = embedded_resources.get_resource_info()
                self.logger.info(f"ECharts主库: {info['echarts_size']} 字符")
                self.logger.info(f"词云插件: {info['wordcloud_size']} 字符")
                self.logger.info(f"总大小: {info['total_size']} 字符")
                
                self.fixes_applied.append("启用嵌入式资源模式")
                return True
            else:
                self.logger.warning("⚠️ 嵌入式资源加载失败，将使用传统离线模式")
                return False
                
        except Exception as e:
            self.logger.error(f"设置嵌入式资源失败: {e}")
            return False
    
    def setup_offline_charts(self) -> bool:
        """设置离线图表配置"""
        self.logger.info("🔧 设置离线图表配置...")
        
        try:
            if OfflineChartConfig is None:
                self.logger.error("无法导入OfflineChartConfig，跳过离线图表设置")
                return False
            
            # 创建离线配置实例
            offline_config = OfflineChartConfig()
            
            # 设置离线模式
            if offline_config.setup_offline_mode():
                self.logger.info("✅ 离线图表配置成功")
                
                # 检查资源状态
                status = offline_config.check_offline_resources()
                if status.get("offline_ready", False):
                    self.logger.info("✅ 离线资源就绪")
                else:
                    missing = status.get("missing_files", [])
                    if missing:
                        self.logger.warning(f"⚠️ 缺少资源文件: {', '.join(missing)}")
                
                self.fixes_applied.append("配置离线图表模式")
                return True
            else:
                self.logger.warning("⚠️ 离线图表配置失败")
                return False
                
        except Exception as e:
            self.logger.error(f"设置离线图表配置失败: {e}")
            return False
    
    def create_webengine_config(self) -> bool:
        """创建WebEngine离线配置文件"""
        self.logger.info("🔧 创建WebEngine离线配置...")
        
        try:
            config_dir = self.project_root / "config"
            config_dir.mkdir(exist_ok=True)
            
            webengine_config = {
                "offline_mode": True,
                "disable_network": True,
                "disable_gpu": True,
                "disable_sandbox": True,
                "chromium_flags": [
                    "--no-sandbox",
                    "--disable-web-security", 
                    "--disable-features=VizDisplayCompositor",
                    "--disable-gpu",
                    "--disable-software-rasterizer",
                    "--disable-background-networking",
                    "--in-process-gpu",
                    "--disable-dev-shm-usage",
                    "--disable-network-service",
                    "--disable-features=NetworkService"
                ],
                "user_data_dir": "temp/webengine_data",
                "force_local_resources": True
            }
            
            config_file = config_dir / "webengine_offline_config.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(webengine_config, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"✅ WebEngine配置文件已创建: {config_file}")
            self.fixes_applied.append("创建WebEngine离线配置文件")
            return True
            
        except Exception as e:
            self.logger.error(f"创建WebEngine配置失败: {e}")
            return False
    
    def run_full_check(self) -> Dict[str, any]:
        """运行完整检查"""
        self.logger.info("🚀 开始离线资源完整性检查...")
        
        results = {
            "icons": self.check_icon_resources(),
            "javascript": self.check_javascript_resources(),
            "issues_count": 0,
            "fixes_count": 0,
            "overall_status": "unknown"
        }
        
        results["issues_count"] = len(self.issues_found)
        
        # 确定整体状态
        if results["icons"]["status"] == "error" or results["javascript"]["status"] == "error":
            results["overall_status"] = "error"
        elif results["icons"]["status"] == "warning" or results["javascript"]["status"] == "warning":
            results["overall_status"] = "warning"
        else:
            results["overall_status"] = "ok"
        
        return results
    
    def run_full_fix(self) -> bool:
        """运行完整修复"""
        self.logger.info("🔧 开始离线资源修复...")
        
        success_count = 0
        total_fixes = 4
        
        # 1. 创建缺失图标
        if self.create_missing_icons():
            success_count += 1
        
        # 2. 设置嵌入式资源
        if self.setup_embedded_resources():
            success_count += 1
        
        # 3. 设置离线图表
        if self.setup_offline_charts():
            success_count += 1
        
        # 4. 创建WebEngine配置
        if self.create_webengine_config():
            success_count += 1
        
        self.logger.info(f"修复完成: {success_count}/{total_fixes} 项成功")
        
        if self.fixes_applied:
            self.logger.info("应用的修复:")
            for fix in self.fixes_applied:
                self.logger.info(f"  ✅ {fix}")
        
        return success_count >= total_fixes // 2  # 至少一半成功才算成功
    
    def generate_report(self) -> str:
        """生成修复报告"""
        report = []
        report.append("=" * 60)
        report.append("离线资源修复报告")
        report.append("=" * 60)
        report.append(f"项目路径: {self.project_root}")
        report.append(f"检查时间: {self._get_current_time()}")
        report.append("")
        
        if self.issues_found:
            report.append("发现的问题:")
            for issue in self.issues_found:
                report.append(f"  ❌ {issue}")
            report.append("")
        
        if self.fixes_applied:
            report.append("应用的修复:")
            for fix in self.fixes_applied:
                report.append(f"  ✅ {fix}")
            report.append("")
        
        report.append("建议:")
        report.append("1. 使用 run1_fixed.bat 启动应用程序")
        report.append("2. 如果图表仍显示异常，运行 python tools/download_offline_resources.py")
        report.append("3. 如果图标仍显示异常，检查防病毒软件是否阻止文件访问")
        report.append("4. 确保项目目录具有完整的读写权限")
        report.append("")
        report.append("=" * 60)
        
        return "\n".join(report)
    
    def _get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def main():
    """主函数"""
    print("🚀 离线资源修复工具")
    print("=" * 50)
    
    fixer = OfflineResourceFixer()
    
    # 运行检查
    print("\n📋 第1步: 运行完整性检查...")
    results = fixer.run_full_check()
    
    print(f"\n📊 检查结果:")
    print(f"  图标资源: {results['icons']['status']}")
    print(f"  JavaScript资源: {results['javascript']['status']}")
    print(f"  发现问题: {results['issues_count']} 个")
    print(f"  整体状态: {results['overall_status']}")
    
    # 如果有问题，运行修复
    if results["overall_status"] != "ok":
        print(f"\n🔧 第2步: 运行自动修复...")
        if fixer.run_full_fix():
            print("✅ 修复完成")
        else:
            print("⚠️ 部分修复失败")
    else:
        print("\n✅ 无需修复，资源状态良好")
    
    # 生成报告
    print(f"\n📄 第3步: 生成修复报告...")
    report = fixer.generate_report()
    print(report)
    
    # 保存报告到文件
    report_file = fixer.project_root / "temp" / "offline_resource_fix_report.txt"
    report_file.parent.mkdir(exist_ok=True)
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    print(f"📄 报告已保存到: {report_file}")
    
    print("\n" + "=" * 50)
    print("修复完成！建议使用 run1_fixed.bat 启动应用程序。")


if __name__ == "__main__":
    main()
